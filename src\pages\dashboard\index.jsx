import { Fragment, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button, Col, Row, Badge } from 'react-bootstrap';
import useGeneralStore from 'store/generalStore.js';
import useSearchStore from 'store/searchStore.js';
import DossierService from 'service/api/dossierService.js';
import { convertToPersianNumbers } from '../../utils/helper';

const Index = () => {
  const navigate = useNavigate();
  const { setData } = useGeneralStore();
  const { clearFilter } = useSearchStore();
  const [statusCounts, setStatusCounts] = useState({});

  // Case status definitions with icons and labels
  const caseStatuses = [
    { id: 'draft', label: 'پیش نویس', icon: 'fa fa-edit', color: '#6c757d' },
    { id: 'arbitration', label: 'داوری و کارشناسی', icon: 'fa fa-gavel' },
    { id: 'workgroup_meeting', label: 'طرح در جلسه کارگروه', icon: 'fa fa-users' },
    { id: 'primary_opinion', label: 'صدور نظریه بدوی کارگروه موسسه', icon: 'fa fa-file-text' },
    { id: 'appeal_request', label: 'درخواست تجدید نظر و ارسال به کارگروه وزارتی', icon: 'fa fa-refresh' },
    { id: 'ministry_opinion', label: 'صدور نظریه کارگروه وزارتی', icon: 'fa fa-building' },
    { id: 'final_opinion', label: 'صدور نظریه نهایی کارگروه موسسه', icon: 'fa fa-check-circle' },
    { id: 'referral', label: 'ارجاع به مراجع ذیصلاح', icon: 'fa fa-exclamation-circle' },
    { id: 'verdict', label: 'صدور حکم/رای مراجع ذیصلاح', icon: 'fa fa-legal' },
    { id: 'completed', label: 'خاتمه یافته', icon: 'fa fa-flag-checkered' },
  ];

  useEffect(() => {
    setData({ pageTitle: 'کارتابل' });
    clearFilter();
    fetchStatusCounts();
  }, []);

  // Fetch counts for each status
  const fetchStatusCounts = async () => {
    try {
      const response = await DossierService.getList();
      const dossiers = response?.data?.data?.dossiers || [];

      // Initialize counts for all statuses
      const counts = {
        draft: 0,
        arbitration: 0,
        workgroup_meeting: 0,
        primary_opinion: 0,
        appeal_request: 0,
        ministry_opinion: 0,
        final_opinion: 0,
        referral: 0,
        verdict: 0,
        completed: 0,
      };

      // Count dossiers by status
      dossiers.forEach((dossier) => {
        const status = dossier.dossier_status;
        if (status in counts) {
          counts[status]++;
        }
      });

      setStatusCounts(counts);
    } catch (error) {
      console.error('Error fetching dossier counts:', error);
      // Set empty counts on error
      setStatusCounts({
        draft: 0,
        arbitration: 0,
        workgroup_meeting: 0,
        primary_opinion: 0,
        appeal_request: 0,
        ministry_opinion: 0,
        final_opinion: 0,
        referral: 0,
        verdict: 0,
        completed: 0,
      });
    }
  };

  // Navigate to search page with specific status filter
  const navigateToSearch = (statusId) => {
    navigate('/app/inquiry/', {
      state: { filter: { status: statusId } },
    });
  };

  return (
    <Fragment>
      {/* Case Status Buttons */}
      <Row className="mg-t-20">
        <Col sm={12}>
          <h4 className="mg-b-20">وضعیت پرونده ها</h4>
        </Col>
        {caseStatuses.map((status) => (
          <Col key={status.id} xs={12} sm={6} md={4} lg={3} className="mg-b-20">
            <Button
              className="w-100 d-flex flex-column align-items-center justify-content-center"
              style={{
                height: '150px',
                padding: '10px',
                backgroundColor: '#f8f9fa',
                border: '1px solid #dee2e6',
                borderRadius: '10px',
                color: '#495057',
              }}
              onClick={() => navigateToSearch(status.id)}
            >
              <i className={`${status.icon}`} style={{ fontSize: '2.6rem', marginBottom: '16px' }}></i>
              <div className="d-flex align-items-center">
                <span>{status.label}</span>
                <Badge bg="primary" className="ms-2" style={{ fontSize: '0.8rem' }}>
                  {convertToPersianNumbers(statusCounts[status.id]) || '۰'}
                </Badge>
              </div>
            </Button>
          </Col>
        ))}
      </Row>
    </Fragment>
  );
};

export default Index;
