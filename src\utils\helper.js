export function convertToPersianNumbers(inputString = '') {
  const persianNumbers = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];

  return inputString.toString().replace(/[0-9]/g, (digit) => {
    return persianNumbers[parseInt(digit)];
  });
}

export const convertToEnglishNumbers = (number) => {
  const persianNumbers = [/۰/g, /۱/g, /۲/g, /۳/g, /۴/g, /۵/g, /۶/g, /۷/g, /۸/g, /۹/g],
    arabicNumbers = [/٠/g, /١/g, /٢/g, /٣/g, /٤/g, /٥/g, /٦/g, /٧/g, /٨/g, /٩/g];

  if (typeof number === 'string') {
    for (let i = 0; i < 10; i++) {
      number = number.replace(persianNumbers[i], i).replace(arabicNumbers[i], i);
    }
  }
  return number;
};

export const parseTimeToPersian = (data) => {
  // const [date, time] = data.includes("T")
  //   ? data.slice(0, data.indexOf("+")).split("T")
  //   : data.split(" ");
  // const [year, month, day] = date.split("-");
  // const [hour, minute, second] = time.split(":");
  return new Date(data)
    .toLocaleDateString('fa-IR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      // hour: "2-digit",
      // minute: "2-digit",
      // second: "2-digit",
    })
    .split(', ')
    .reverse()
    .join(' - ')
    .replaceAll('/', '٫');
};
export const parseTimeToPersian2 = (data) => {
  // const [date, time] = data.includes("T")
  //   ? data.slice(0, data.indexOf("+")).split("T")
  //   : data.split(" ");
  // const [year, month, day] = date.split("-");
  // const [hour, minute, second] = time.split(":");
  return new Date(data)
    .toLocaleDateString('fa-IR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    })
    .split(', ')
    .reverse()
    .join(' - ')
    .replaceAll('/', '٫');
};

export const fixPercent = (percent, point = 2) => {
  if (!percent) return '0';
  return (percent * 100).toFixed(point);
};

export const fixPercentToShow = (percent, point) => {
  return convertToPersianNumbers(fixPercent(percent, point)) + '٪';
};

export const jsonToQueryString = (json) => {
  const params = new URLSearchParams(json);
  return params.toString();
};

export const formatDate = (date) => {
  if (!date) return null;
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

export function formatShortNumber(number) {
  return new Intl.NumberFormat('en', {
    notation: 'compact',
    maximumFractionDigits: 1,
  }).format(number);
}

export function scaleNumber(num) {
  if (num === undefined) return 0;
  return (num + 1) * 50;
}

export const RateColorMap = [
  { label: 'خطرناک', color: '#F34343' },
  { label: 'قانون‌گریز', color: '#FD7E14' },
  { label: 'عادی', color: '#E3AB00' },
  { label: 'قانونمند', color: '#2FAD99' },
  { label: 'ویژه', color: '#17d535' },
];
